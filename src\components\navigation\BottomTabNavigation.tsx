'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { 
  HomeIcon, 
  MusicalNoteIcon, 
  BeakerIcon, 
  ChartBarIcon, 
  UserIcon 
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  MusicalNoteIcon as MusicalNoteIconSolid,
  BeakerIcon as BeakerIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  UserIcon as UserIconSolid
} from '@heroicons/react/24/solid';

interface TabItem {
  id: string;
  label: string;
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  iconSolid: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  ariaLabel: string;
}

const tabs: TabItem[] = [
  {
    id: 'home',
    label: 'Home',
    href: '/',
    icon: HomeIcon,
    iconSolid: HomeIconSolid,
    ariaLabel: 'Navigate to Home dashboard'
  },
  {
    id: 'audio',
    label: 'Audio Lab',
    href: '/audio',
    icon: MusicalNoteIcon,
    iconSolid: MusicalNoteIconSolid,
    ariaLabel: 'Navigate to Audio Engine and Shruti Explorer'
  },
  {
    id: 'nature',
    label: 'Nature',
    href: '/nature',
    icon: BeakerIcon,
    iconSolid: BeakerIconSolid,
    ariaLabel: 'Navigate to Natural Pattern Explorer'
  },
  {
    id: 'patterns',
    label: 'Patterns',
    href: '/patterns',
    icon: ChartBarIcon,
    iconSolid: ChartBarIconSolid,
    ariaLabel: 'Navigate to Pattern Analysis and Visualizations'
  },
  {
    id: 'profile',
    label: 'Profile',
    href: '/profile',
    icon: UserIcon,
    iconSolid: UserIconSolid,
    ariaLabel: 'Navigate to Settings and Profile'
  }
];

export function BottomTabNavigation() {
  const pathname = usePathname();

  const isActiveTab = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <nav 
      className="fixed bottom-0 left-0 right-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-t border-gray-200 dark:border-gray-700 safe-area-pb"
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="flex items-center justify-around px-2 py-1">
        {tabs.map((tab) => {
          const isActive = isActiveTab(tab.href);
          const IconComponent = isActive ? tab.iconSolid : tab.icon;
          
          return (
            <Link
              key={tab.id}
              href={tab.href}
              className={`
                flex flex-col items-center justify-center min-h-touch px-2 py-1 rounded-lg
                transition-all duration-200 ease-out
                ${isActive 
                  ? 'text-primary-500 bg-primary-50 dark:bg-primary-900/20' 
                  : 'text-gray-600 dark:text-gray-400 hover:text-primary-500 hover:bg-gray-50 dark:hover:bg-gray-800'
                }
                focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
                active:scale-95
              `}
              aria-label={tab.ariaLabel}
              aria-current={isActive ? 'page' : undefined}
            >
              <IconComponent 
                className={`w-6 h-6 mb-0.5 transition-transform duration-200 ${
                  isActive ? 'scale-110' : 'scale-100'
                }`} 
              />
              <span className={`text-xs font-medium transition-colors duration-200 ${
                isActive ? 'text-primary-600 dark:text-primary-400' : ''
              }`}>
                {tab.label}
              </span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}

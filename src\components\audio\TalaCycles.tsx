'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { MobileCard, MobileButton } from '@/components/layout/MobileAppLayout';

interface TalaBeat {
  position: number;
  type: 'sam' | 'tali' | 'khali';
  sound: string;
  emphasis: 'strong' | 'medium' | 'weak';
}

interface Tala {
  id: string;
  name: string;
  englishName: string;
  beats: number;
  structure: TalaBeat[];
  description: string;
  commonUsage: string;
}

const talas: Tala[] = [
  {
    id: 'teentaal',
    name: 'तीनताल',
    englishName: 'Teentaal',
    beats: 16,
    structure: [
      { position: 1, type: 'sam', sound: 'Dha', emphasis: 'strong' },
      { position: 2, type: 'tali', sound: 'Dha', emphasis: 'medium' },
      { position: 3, type: 'tali', sound: 'Tin', emphasis: 'medium' },
      { position: 4, type: 'tali', sound: 'Ta', emphasis: 'medium' },
      { position: 5, type: 'tali', sound: 'Dha', emphasis: 'medium' },
      { position: 6, type: 'tali', sound: 'Dha', emphasis: 'medium' },
      { position: 7, type: 'tali', sound: 'Tin', emphasis: 'medium' },
      { position: 8, type: 'tali', sound: 'Ta', emphasis: 'medium' },
      { position: 9, type: 'khali', sound: 'Tin', emphasis: 'weak' },
      { position: 10, type: 'khali', sound: 'Tin', emphasis: 'weak' },
      { position: 11, type: 'khali', sound: 'Ta', emphasis: 'weak' },
      { position: 12, type: 'khali', sound: 'Ta', emphasis: 'weak' },
      { position: 13, type: 'tali', sound: 'Dha', emphasis: 'medium' },
      { position: 14, type: 'tali', sound: 'Dha', emphasis: 'medium' },
      { position: 15, type: 'tali', sound: 'Tin', emphasis: 'medium' },
      { position: 16, type: 'tali', sound: 'Ta', emphasis: 'medium' }
    ],
    description: 'The most common tala in Hindustani classical music with 16 beats.',
    commonUsage: 'Khyal, Thumri, Bhajan'
  },
  {
    id: 'ektaal',
    name: 'एकताल',
    englishName: 'Ektaal',
    beats: 12,
    structure: [
      { position: 1, type: 'sam', sound: 'Dhin', emphasis: 'strong' },
      { position: 2, type: 'tali', sound: 'Dhin', emphasis: 'medium' },
      { position: 3, type: 'khali', sound: 'Dha', emphasis: 'weak' },
      { position: 4, type: 'khali', sound: 'Ge', emphasis: 'weak' },
      { position: 5, type: 'tali', sound: 'Tin', emphasis: 'medium' },
      { position: 6, type: 'tali', sound: 'Ta', emphasis: 'medium' },
      { position: 7, type: 'tali', sound: 'Ka', emphasis: 'medium' },
      { position: 8, type: 'tali', sound: 'Dha', emphasis: 'medium' },
      { position: 9, type: 'tali', sound: 'Tin', emphasis: 'medium' },
      { position: 10, type: 'tali', sound: 'Ta', emphasis: 'medium' },
      { position: 11, type: 'tali', sound: 'Ka', emphasis: 'medium' },
      { position: 12, type: 'tali', sound: 'Dhin', emphasis: 'medium' }
    ],
    description: 'A 12-beat tala commonly used in slow compositions.',
    commonUsage: 'Vilambit Khyal, Dhrupad'
  },
  {
    id: 'jhaptaal',
    name: 'झपताल',
    englishName: 'Jhaptaal',
    beats: 10,
    structure: [
      { position: 1, type: 'sam', sound: 'Dhin', emphasis: 'strong' },
      { position: 2, type: 'tali', sound: 'Na', emphasis: 'medium' },
      { position: 3, type: 'tali', sound: 'Dhin', emphasis: 'medium' },
      { position: 4, type: 'tali', sound: 'Dhin', emphasis: 'medium' },
      { position: 5, type: 'tali', sound: 'Na', emphasis: 'medium' },
      { position: 6, type: 'khali', sound: 'Tin', emphasis: 'weak' },
      { position: 7, type: 'khali', sound: 'Na', emphasis: 'weak' },
      { position: 8, type: 'tali', sound: 'Dhin', emphasis: 'medium' },
      { position: 9, type: 'tali', sound: 'Dhin', emphasis: 'medium' },
      { position: 10, type: 'tali', sound: 'Na', emphasis: 'medium' }
    ],
    description: 'A 10-beat tala with asymmetrical structure.',
    commonUsage: 'Medium tempo compositions'
  }
];

export function TalaCycles() {
  const [selectedTala, setSelectedTala] = useState<Tala>(talas[0] || {
    name: 'Teentaal',
    beats: 16,
    pattern: [
      { position: 1, type: 'sam', sound: 'dha', emphasis: 'strong' },
      { position: 2, type: 'tali', sound: 'dha', emphasis: 'medium' },
      { position: 3, type: 'tali', sound: 'ti', emphasis: 'weak' },
      { position: 4, type: 'tali', sound: 'ta', emphasis: 'weak' }
    ],
    description: 'Most common 16-beat cycle in Hindustani music'
  });
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentBeat, setCurrentBeat] = useState(0);
  const [tempo, setTempo] = useState(120); // BPM
  const [cycleCount, setCycleCount] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const startTala = () => {
    if (isPlaying) return;
    
    setIsPlaying(true);
    setCurrentBeat(0);
    setCycleCount(0);
    
    const beatDuration = (60 / tempo) * 1000; // Convert BPM to milliseconds
    
    intervalRef.current = setInterval(() => {
      setCurrentBeat(prev => {
        const nextBeat = (prev + 1) % selectedTala.beats;
        if (nextBeat === 0) {
          setCycleCount(count => count + 1);
        }
        return nextBeat;
      });
    }, beatDuration);
  };

  const stopTala = () => {
    setIsPlaying(false);
    setCurrentBeat(0);
    setCycleCount(0);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const pauseTala = () => {
    setIsPlaying(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const getBeatColor = (beat: TalaBeat, isActive: boolean) => {
    if (isActive) {
      return beat.type === 'sam' ? 'bg-primary-500 text-white' :
             beat.type === 'tali' ? 'bg-secondary-500 text-white' :
             'bg-gray-400 text-white';
    }
    return beat.type === 'sam' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' :
           beat.type === 'tali' ? 'bg-secondary-100 dark:bg-secondary-900 text-secondary-700 dark:text-secondary-300' :
           'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400';
  };

  const renderTalaWheel = () => {
    const centerX = 150;
    const centerY = 150;
    const radius = 100;
    
    return (
      <div className="aspect-square max-w-sm mx-auto relative">
        <svg viewBox="0 0 300 300" className="w-full h-full">
          {/* Outer circle */}
          <circle
            cx={centerX}
            cy={centerY}
            r={radius + 20}
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="text-gray-300 dark:text-gray-600"
          />
          
          {/* Beat positions */}
          {selectedTala.structure.map((beat, index) => {
            const angle = (index * 360) / selectedTala.beats - 90; // Start from top
            const radian = (angle * Math.PI) / 180;
            const x = centerX + radius * Math.cos(radian);
            const y = centerY + radius * Math.sin(radian);
            const isActive = currentBeat === index;
            
            return (
              <g key={beat.position}>
                {/* Beat circle */}
                <circle
                  cx={x}
                  cy={y}
                  r={isActive ? "12" : "8"}
                  fill={beat.type === 'sam' ? '#1e40af' : 
                        beat.type === 'tali' ? '#f59e0b' : '#6b7280'}
                  stroke={isActive ? "#ffffff" : "none"}
                  strokeWidth="3"
                  className="transition-all duration-200"
                />
                
                {/* Beat number */}
                <text
                  x={x}
                  y={y + 4}
                  textAnchor="middle"
                  className="text-xs font-bold fill-white pointer-events-none"
                >
                  {beat.position}
                </text>
                
                {/* Beat label */}
                <text
                  x={x}
                  y={y - 20}
                  textAnchor="middle"
                  className="text-xs font-medium fill-current text-gray-700 dark:text-gray-300 pointer-events-none"
                >
                  {beat.sound}
                </text>
              </g>
            );
          })}
          
          {/* Center info */}
          <circle
            cx={centerX}
            cy={centerY}
            r="50"
            fill="currentColor"
            className="text-gray-50 dark:text-gray-800"
          />
          <text
            x={centerX}
            y={centerY - 10}
            textAnchor="middle"
            className="text-sm font-bold fill-current text-gray-700 dark:text-gray-300"
          >
            {tempo}BPM
          </text>
          <text
            x={centerX}
            y={centerY + 10}
            textAnchor="middle"
            className="text-xs fill-current text-gray-600 dark:text-gray-400"
          >
            Cycle {cycleCount}
          </text>
        </svg>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Tala Selector */}
      <MobileCard>
        <div className="relative">
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            <div className="text-left">
              <div className="font-medium text-gray-900 dark:text-white">
                {selectedTala.englishName} ({selectedTala.beats} beats)
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {selectedTala.name}
              </div>
            </div>
            <ChevronDownIcon className={`w-5 h-5 text-gray-400 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
          </button>
          
          {isDropdownOpen && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10">
              {talas.map((tala) => (
                <button
                  key={tala.id}
                  onClick={() => {
                    setSelectedTala(tala);
                    setIsDropdownOpen(false);
                    stopTala(); // Reset when changing tala
                  }}
                  className="w-full p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                >
                  <div className="font-medium text-gray-900 dark:text-white">
                    {tala.englishName} ({tala.beats} beats)
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {tala.name}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </MobileCard>

      {/* Tala Wheel */}
      <MobileCard>
        {renderTalaWheel()}
      </MobileCard>

      {/* Tempo Control */}
      <MobileCard>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Tempo
            </label>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {tempo} BPM
            </span>
          </div>
          <input
            type="range"
            min="60"
            max="200"
            step="5"
            value={tempo}
            onChange={(e) => setTempo(Number(e.target.value))}
            disabled={isPlaying}
            className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>60 BPM</span>
            <span>200 BPM</span>
          </div>
        </div>
      </MobileCard>

      {/* Current Beat Info */}
      {isPlaying && (
        <MobileCard>
          <div className="text-center space-y-2">
            <h3 className="font-semibold text-gray-900 dark:text-white">
              Beat {currentBeat + 1}: {selectedTala.structure[currentBeat]?.sound}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {selectedTala.structure[currentBeat]?.type === 'sam' ? 'Sam (Strong)' :
               selectedTala.structure[currentBeat]?.type === 'tali' ? 'Tali (Medium)' :
               'Khali (Weak)'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-500">
              Cycle {cycleCount + 1}
            </p>
          </div>
        </MobileCard>
      )}

      {/* Playback Controls */}
      <MobileCard>
        <div className="flex items-center justify-center space-x-4">
          <MobileButton
            variant={isPlaying ? "secondary" : "primary"}
            size="lg"
            onClick={startTala}
            disabled={isPlaying}
          >
            <PlayIcon className="w-5 h-5 mr-2" />
            Play
          </MobileButton>
          
          <MobileButton
            variant="outline"
            size="lg"
            onClick={pauseTala}
            disabled={!isPlaying}
          >
            <PauseIcon className="w-5 h-5 mr-2" />
            Pause
          </MobileButton>
          
          <MobileButton
            variant="outline"
            size="lg"
            onClick={stopTala}
          >
            <StopIcon className="w-5 h-5 mr-2" />
            Stop
          </MobileButton>
        </div>
      </MobileCard>

      {/* Tala Information */}
      <MobileCard>
        <div className="space-y-3">
          <h3 className="font-semibold text-gray-900 dark:text-white">
            About {selectedTala.englishName}
          </h3>
          
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Beats: </span>
              <span className="text-gray-600 dark:text-gray-400">{selectedTala.beats}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Common Usage: </span>
              <span className="text-gray-600 dark:text-gray-400">{selectedTala.commonUsage}</span>
            </div>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              {selectedTala.description}
            </p>
          </div>
        </div>
      </MobileCard>

      {/* Beat Structure */}
      <MobileCard>
        <div className="space-y-3">
          <h3 className="font-semibold text-gray-900 dark:text-white">
            Beat Structure
          </h3>
          
          <div className="grid grid-cols-4 gap-2">
            {selectedTala.structure.map((beat, index) => (
              <div
                key={beat.position}
                className={`p-2 rounded text-center text-xs font-medium transition-all duration-200 ${
                  getBeatColor(beat, currentBeat === index)
                }`}
              >
                <div className="font-bold">{beat.position}</div>
                <div>{beat.sound}</div>
                <div className="text-xs opacity-75">
                  {beat.type === 'sam' ? 'Sam' : beat.type === 'tali' ? 'Tali' : 'Khali'}
                </div>
              </div>
            ))}
          </div>
        </div>
      </MobileCard>
    </div>
  );
}

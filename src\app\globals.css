@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Dynamic Theming */
:root {
  --cosmic-primary: #667eea;
  --cosmic-secondary: #764ba2;
  --nature-ocean: #0ea5e9;
  --nature-forest: #16a34a;
  --raga-morning: #fbbf24;
  --raga-evening: #8b5cf6;
  
  /* Audio visualization colors */
  --waveform-color: #6366f1;
  --spectrum-color: #8b5cf6;
  --pattern-match-color: #16a34a;
  
  /* Animation durations */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
}

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply text-white;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variant-numeric: oldstyle-nums;
  }
  
  /* Focus styles for accessibility */
  *:focus {
    @apply outline-none ring-2 ring-cosmic-400 ring-offset-2 ring-offset-transparent;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-white/10 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-white/30 rounded-full hover:bg-white/50;
  }
}

/* Component styles */
@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl;
  }
  
  .glass-strong {
    @apply bg-white/20 backdrop-blur-lg border border-white/30 rounded-xl;
  }
  
  /* Button variants */
  .btn-primary {
    @apply bg-gradient-to-r from-cosmic-500 to-cosmic-600 hover:from-cosmic-600 hover:to-cosmic-700 
           text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 
           shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 
           focus:ring-2 focus:ring-cosmic-400 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-white/10 hover:bg-white/20 text-white font-medium px-6 py-3 rounded-lg 
           transition-all duration-300 border border-white/20 hover:border-white/30
           backdrop-blur-sm hover:backdrop-blur-md;
  }
  
  .btn-nature {
    @apply bg-gradient-to-r from-nature-forest to-nature-ocean hover:from-green-600 hover:to-blue-600
           text-white font-medium px-6 py-3 rounded-lg transition-all duration-300
           shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }
  
  /* Audio control styles */
  .audio-control {
    @apply w-12 h-12 rounded-full bg-white/20 hover:bg-white/30 
           flex items-center justify-center transition-all duration-300
           border border-white/20 hover:border-white/40 backdrop-blur-sm;
  }
  
  .audio-control:hover {
    @apply transform scale-105 shadow-lg;
  }
  
  .audio-control:active {
    @apply transform scale-95;
  }
  
  /* Waveform visualization */
  .waveform-container {
    @apply relative w-full h-24 bg-black/20 rounded-lg overflow-hidden border border-white/10;
  }
  
  .waveform-bar {
    @apply bg-gradient-to-t from-cosmic-500 to-cosmic-300 rounded-sm transition-all duration-100;
  }
  
  /* Pattern visualization */
  .pattern-node {
    @apply w-4 h-4 rounded-full bg-cosmic-400 border-2 border-white/50 
           transition-all duration-300 hover:scale-125 cursor-pointer;
  }
  
  .pattern-connection {
    @apply stroke-cosmic-300 stroke-2 opacity-60 hover:opacity-100 transition-opacity duration-300;
  }
  
  /* Card styles */
  .nature-card {
    @apply glass p-6 hover:bg-white/15 transition-all duration-300 
           transform hover:-translate-y-1 hover:shadow-2xl cursor-pointer
           border-l-4 border-l-nature-ocean;
  }
  
  .music-card {
    @apply glass p-6 hover:bg-white/15 transition-all duration-300
           transform hover:-translate-y-1 hover:shadow-2xl cursor-pointer
           border-l-4 border-l-raga-evening;
  }
  
  /* Loading animations */
  .loading-pulse {
    @apply animate-pulse bg-gradient-to-r from-white/10 to-white/20 rounded;
  }
  
  .loading-spin {
    @apply animate-spin-slow;
  }
  
  /* Text styles */
  .text-gradient-cosmic {
    @apply bg-gradient-to-r from-cosmic-300 to-cosmic-500 bg-clip-text text-transparent;
  }
  
  .text-gradient-nature {
    @apply bg-gradient-to-r from-nature-forest to-nature-ocean bg-clip-text text-transparent;
  }
  
  .text-gradient-raga {
    @apply bg-gradient-to-r from-raga-morning to-raga-evening bg-clip-text text-transparent;
  }
}

/* Utility classes */
@layer utilities {
  /* Mobile-first safe area utilities */
  .safe-area-pt {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-pl {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-pr {
    padding-right: env(safe-area-inset-right);
  }

  /* Touch-friendly minimum sizes */
  .min-h-touch {
    min-height: 44px;
  }

  .min-w-touch {
    min-width: 44px;
  }

  /* Custom slider styling */
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #1e40af;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #1e40af;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }
  
  /* Responsive text utilities */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }
  
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }
  
  .text-responsive-base {
    @apply text-base sm:text-lg;
  }
  
  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }
  
  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }
  
  /* Spacing utilities */
  .space-y-responsive {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }
  
  /* Grid utilities */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Audio visualization specific styles */
.frequency-bar {
  transition: height 50ms ease-out;
}

.pattern-highlight {
  @apply ring-2 ring-yellow-400 ring-opacity-75 animate-pulse;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass {
    @apply bg-black/80 border-white;
  }
  
  .btn-primary {
    @apply bg-blue-600 border-2 border-white;
  }
}

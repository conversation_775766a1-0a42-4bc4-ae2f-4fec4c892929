'use client';

import React, { useState } from 'react';
import { 
  PlayIcon, 
  ChartBarIcon, 
  MusicalNoteIcon, 
  BeakerIcon,
  ChartPieIcon,
  AcademicCapIcon,
  Bars3Icon,
  MoonIcon,
  SunIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { MobileCard, MobileButton } from '@/components/layout/MobileAppLayout';

interface DailyDiscovery {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  audioUrl?: string;
  analysisUrl?: string;
  imageUrl?: string;
}

interface QuickAction {
  id: string;
  title: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  href: string;
  color: string;
  description: string;
}

interface FeaturedLearning {
  id: string;
  title: string;
  category: string;
  progress?: number;
}

const quickActions: QuickAction[] = [
  {
    id: 'audio-lab',
    title: 'Audio Lab',
    icon: MusicalNoteIcon,
    href: '/audio',
    color: 'bg-primary-500',
    description: 'Explore 22-shruti system and ragas'
  },
  {
    id: 'nature-explorer',
    title: 'Nature Explorer',
    icon: BeakerIcon,
    href: '/nature',
    color: 'bg-nature-forest',
    description: 'Discover natural sound patterns'
  },
  {
    id: 'patterns',
    title: 'Patterns',
    icon: ChartBarIcon,
    href: '/patterns',
    color: 'bg-accent-500',
    description: 'Analyze mathematical relationships'
  },
  {
    id: 'raga-practice',
    title: 'Raga Practice',
    icon: AcademicCapIcon,
    href: '/practice',
    color: 'bg-secondary-500',
    description: 'Practice aroha and avaroha'
  }
];

const featuredLearning: FeaturedLearning[] = [
  {
    id: 'golden-ratio-shells',
    title: 'Golden Ratio in Shell Spirals',
    category: 'Mathematics',
    progress: 65
  },
  {
    id: 'fibonacci-tala',
    title: 'Fibonacci in Tala Cycles',
    category: 'Music Theory',
    progress: 30
  },
  {
    id: 'harmonic-bird-songs',
    title: 'Harmonic Series in Bird Songs',
    category: 'Nature Patterns',
    progress: 85
  }
];

export function HomeDashboard() {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const dailyDiscovery: DailyDiscovery = {
    id: 'ocean-yaman',
    title: 'Ocean Waves ↔ Raga Yaman',
    subtitle: "Today's Discovery",
    description: 'Discover the mathematical harmony between Pacific Ocean wave patterns and the evening raga Yaman.',
    audioUrl: '/audio/ocean-yaman-demo.mp3',
    analysisUrl: '/patterns/ocean-yaman'
  };

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
    // In a real app, this would update the theme context
    document.documentElement.classList.toggle('dark');
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call for new daily content
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  const handleQuickActionPress = (actionId: string) => {
    // Handle quick action navigation
    console.log(`Navigating to: ${actionId}`);
    // In a real app, this would use Next.js router
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="sticky top-0 z-40 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button 
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
              aria-label="Open menu"
            >
              <Bars3Icon className="w-6 h-6 text-gray-700 dark:text-gray-300" />
            </button>
            <div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                Cosmic
              </h1>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button 
              onClick={toggleDarkMode}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
              aria-label="Toggle dark mode"
            >
              {isDarkMode ? (
                <SunIcon className="w-6 h-6 text-gray-700 dark:text-gray-300" />
              ) : (
                <MoonIcon className="w-6 h-6 text-gray-700 dark:text-gray-300" />
              )}
            </button>
            <button 
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
              aria-label="Open settings"
            >
              <Cog6ToothIcon className="w-6 h-6 text-gray-700 dark:text-gray-300" />
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="px-4 py-6 pb-24 space-y-6">
        {/* Welcome Section */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome back, Explorer
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Continue your journey through music and nature
          </p>
        </div>

        {/* Daily Discovery Card */}
        <MobileCard className="bg-gradient-to-br from-primary-500 to-primary-600 text-white border-0">
          <div className="space-y-4">
            <div>
              <p className="text-primary-100 text-sm font-medium mb-1">
                {dailyDiscovery.subtitle}
              </p>
              <h3 className="text-xl font-bold mb-2">
                {dailyDiscovery.title}
              </h3>
              <p className="text-primary-100 text-sm leading-relaxed">
                {dailyDiscovery.description}
              </p>
            </div>
            
            <div className="flex space-x-3">
              <MobileButton
                variant="outline"
                size="sm"
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                <PlayIcon className="w-4 h-4 mr-2" />
                Listen
              </MobileButton>
              <MobileButton
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/10"
              >
                <ChartBarIcon className="w-4 h-4 mr-2" />
                Analyze
              </MobileButton>
            </div>
          </div>
        </MobileCard>

        {/* Quick Actions */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-2 gap-3">
            {quickActions.map((action) => (
              <MobileCard 
                key={action.id}
                className="hover:shadow-md transition-shadow duration-200 cursor-pointer"
              >
                <div className="text-center space-y-3">
                  <div className={`w-12 h-12 ${action.color} rounded-xl flex items-center justify-center mx-auto`}>
                    <action.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                      {action.title}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      {action.description}
                    </p>
                  </div>
                </div>
              </MobileCard>
            ))}
          </div>
        </div>

        {/* Featured Learning */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Featured Learning
          </h3>
          <div className="space-y-3">
            {featuredLearning.map((item) => (
              <MobileCard key={item.id} className="hover:shadow-md transition-shadow duration-200 cursor-pointer">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                      {item.title}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      {item.category}
                    </p>
                    {item.progress && (
                      <div className="mt-2">
                        <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                          <span>Progress</span>
                          <span>{item.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                          <div 
                            className="bg-primary-500 h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${item.progress}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  <ChartPieIcon className="w-5 h-5 text-gray-400 ml-3" />
                </div>
              </MobileCard>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}
